import { requireAdmin } from '@/lib/auth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LogOut, User, Users, Plus } from 'lucide-react'
import Link from 'next/link'
import { AdminDashboard } from '@/components/admin/AdminDashboard'

export default async function AdminPage() {
  const profile = await requireAdmin()

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-primary">
            سحابة المدينة - لوحة الإدارة
          </h1>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <User className="h-4 w-4" />
              {profile.full_name || profile.email}
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link href="/api/auth/signout">
                <LogOut className="h-4 w-4 ml-2" />
                تسجيل الخروج
              </Link>
            </Button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div>
            <h2 className="text-3xl font-bold">مرحباً بك في لوحة الإدارة</h2>
            <p className="text-muted-foreground mt-2">
              إدارة النظام والمستخدمين
            </p>
          </div>

          <AdminDashboard />
        </div>
      </main>
    </div>
  )
}
