// Simple script to create admin user
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://pmhehkizqfyqmxiggozv.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBtaGVoa2l6cWZ5cW14aWdnb3p2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjMyMTc4NywiZXhwIjoyMDY3ODk3Nzg3fQ.SsrBFDf36nTXInvbN3xUlLV50IY7_fs97HO4PjlI_Fc'

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createAdminUser() {
  try {
    console.log('Creating admin user...')
    
    // Create user with admin API
    const { data: user, error: createError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'admin123456',
      email_confirm: true,
      user_metadata: {
        full_name: 'مدير النظام'
      }
    })

    if (createError) {
      console.error('Error creating user:', createError)
      return
    }

    console.log('User created:', user.user.id)

    // Update profile to admin role
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ 
        role: 'admin',
        full_name: 'مدير النظام'
      })
      .eq('id', user.user.id)

    if (updateError) {
      console.error('Error updating profile:', updateError)
      return
    }

    console.log('Admin user created successfully!')
    console.log('Email: <EMAIL>')
    console.log('Password: admin123456')

  } catch (error) {
    console.error('Unexpected error:', error)
  }
}

createAdminUser()
